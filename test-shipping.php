<?php declare(strict_types=1);

/**
 * Test script for shipping API
 * Usage: php test-shipping.php <delivery_mode> <parcel_code>
 * Example: php test-shipping.php ppl 12345678
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Model\Shipping\ShippingService;
use App\Model\Shipping\PplApi;
use App\Model\Shipping\DpdApi;
use App\Model\Shipping\ZasilkovnaApi;
use App\Model\Shipping\BalíkovnaApi;

// Check command line arguments
if ($argc < 3) {
    echo "Usage: php test-shipping.php <delivery_mode> <parcel_code>\n";
    echo "Available delivery modes: ppl, dpd, zasilkovna, balikovna\n";
    echo "Example: php test-shipping.php ppl 12345678\n";
    exit(1);
}

$deliveryMode = $argv[1];
$parcelCode = $argv[2];

// Load configuration (you may need to adjust paths)
$config = [
    'ppl' => [
        'apiUrl' => 'https://myapi.ppl.cz/MyApi.svc?singleWsdl',
        'custId' => '4537599',
        'username' => 'NOV4537599',
        'password' => 'NOV4537599',
        'timeout' => 30,
    ],
    'dpd' => [
        'apiUrl' => 'https://api.dpd.cz/v1/',
        'username' => getenv('DPD_USERNAME') ?: '',
        'password' => getenv('DPD_PASSWORD') ?: '',
        'timeout' => 30,
    ],
    'zasilkovna' => [
        'apiUrl' => 'https://www.zasilkovna.cz/api/soap.wsdl',
        'apiKey' => 'd89c2ca905f729a7d2cca5f14ce4973b',
        'timeout' => 30,
    ],
    'balikovna' => [
        'apiUrl' => 'https://www.balikovna.cz/api/v1/',
        'apiKey' => getenv('BALIKOVNA_API_KEY') ?: '',
        'timeout' => 30,
    ],
];

try {
    echo "Testing shipping API...\n";
    echo "Delivery mode: {$deliveryMode}\n";
    echo "Parcel code: {$parcelCode}\n";
    echo str_repeat('-', 50) . "\n";

    // Create shipping service
    $shippingService = new ShippingService($config);

	// Get carrier name
    $supportedModes = $shippingService->getSupportedDeliveryModes();
    if (isset($supportedModes[$deliveryMode])) {
        echo "\nCarrier: {$supportedModes[$deliveryMode]}\n";
    }

    // Get parcel status
    $status = $shippingService->getStatus($deliveryMode, $parcelCode);
    echo "Parcel status: {$status}\n";

    // Check if delivered
    $isDelivered = $shippingService->isDelivered($deliveryMode, $parcelCode);
    echo "Is delivered: " . ($isDelivered ? 'YES' : 'NO') . "\n";

    echo str_repeat('-', 50) . "\n";
    echo "Test completed successfully!\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
