<?php declare(strict_types=1);

namespace App\Model\Shipping;

use App\Model\Shipping\Exception\PacketNotFoundException;

class ZasilkovnaApi extends AbstractShippingApi
{
    private ?\SoapClient $soapClient = null;
    /**
     * {@inheritdoc}
     */
    public function getStatus(string $parcelCode): string
    {
        $soap = $this->getSoapClient();
		$statusCode = null;
        try {
            // Call SOAP method with separate parameters (not array)
            $result = $soap->packetStatus($this->config['apiKey'], $parcelCode);

            if (!isset($result->statusCode)) {
                throw new PacketNotFoundException("No data found for packet ID {$parcelCode}");
            }

			$statusCode = $result->statusCode;

        } catch (\SoapFault $e) {
            throw new \RuntimeException("Zásilkovna SOAP API error: " . $e->getMessage());
        }


        return $this->mapCarrierStatusToGeneral((string)$statusCode);
    }

    /**
     * {@inheritdoc}
     */
    public function getCarrierName(): string
    {
        return 'Zásilkovna';
    }

    /**
     * {@inheritdoc}
     */
    protected function mapCarrierStatusToGeneral(string $carrierStatus): string
    {
        // Zásilkovna API status mapping based on status codes
        $statusCode = (int)$carrierStatus;

        // Map Zásilkovna status codes to general statuses
        $mapping = [
            1 => self::PARCEL_STATUS_CREATED,        // Packet created
            2 => self::PARCEL_STATUS_PICKED_UP,      // Packet picked up
            3 => self::PARCEL_STATUS_IN_TRANSIT,     // In transit
            4 => self::PARCEL_STATUS_OUT_FOR_DELIVERY, // Ready for pickup
            5 => self::PARCEL_STATUS_DELIVERED,      // Delivered
            6 => self::PARCEL_STATUS_RETURNED,       // Returned
            7 => self::PARCEL_STATUS_CANCELLED,      // Cancelled
        ];

        return $mapping[$statusCode] ?? self::PARCEL_STATUS_UNKNOWN;
    }

    /**
     * {@inheritdoc}
     */
    protected function getParcelInfoFromApi(string $parcelCode): array
    {
        $packetInfo = $this->getPacketInfo($parcelCode);
        return ['packet' => $packetInfo];
    }

    /**
     * Get SOAP client instance
     */
    private function getSoapClient(): \SoapClient
    {
        if (!$this->soapClient) {
            $this->soapClient = new \SoapClient($this->config['apiUrl']);
        }

        return $this->soapClient;
    }

    /**
     * Get packet information from Zásilkovna SOAP API
     */
    private function getPacketInfo(string $packetId)
    {
        $soap = $this->getSoapClient();
        try {
            // Call SOAP method with separate parameters (not array)
            $result = $soap->packetStatus($this->config['apiKey'], $packetId);

            if (!isset($result->statusCode)) {
                throw new PacketNotFoundException("No data found for packet ID {$packetId}");
            }

            return $result->statusCode;

        } catch (\SoapFault $e) {
            throw new \RuntimeException("Zásilkovna SOAP API error: " . $e->getMessage());
        }
    }

    /**
     * Get latest status from packet info
     */
    private function getLatestPacketStatus($packetInfo): int
    {
        // In Zásilkovna API, the current status is directly available
        return (int)($packetInfo->statusId ?? 0);
    }

    /**
     * {@inheritdoc}
     */
    protected function getAllParcelStatuses(string $parcelCode): array
    {
        $packetInfo = $this->getPacketInfo($parcelCode);

        // Get status history from packet info
        $statusHistory = $packetInfo->statusHistory ?? [];

        if (empty($statusHistory)) {
            // If no history, return at least current status
            $currentStatus = (string)($packetInfo->statusId ?? 0);
            return $currentStatus !== '0' ? [$currentStatus] : [];
        }

        // Convert to array if single status
        if (!is_array($statusHistory)) {
            $statusHistory = [$statusHistory];
        }

        // Extract status IDs
        $statusIds = [];
        foreach ($statusHistory as $status) {
            $statusIds[] = (string)($status->statusId ?? 0);
        }

        return $statusIds;
    }
}
